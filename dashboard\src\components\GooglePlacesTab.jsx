import { useState } from 'react'
import { MapPin, Loader2, Info } from 'lucide-react'

const GooglePlacesTab = ({ onSearch, loading }) => {
  const [formData, setFormData] = useState({
    search_type: 'text_search',
    query: '',
    location: '',
    radius: 1000,
    place_type: '',
    fields: ['name', 'formatted_address', 'formatted_phone_number', 'website']
  })

  const searchTypes = [
    { value: 'text_search', label: 'Text Search', description: 'Search by query text' },
    { value: 'nearby_search', label: 'Nearby Search', description: 'Search near a location' }
  ]

  const placeTypes = [
    { value: '', label: 'All Types' },
    { value: 'restaurant', label: 'Restaurant' },
    { value: 'food', label: 'Food' },
    { value: 'store', label: 'Store' },
    { value: 'lodging', label: 'Lodging' },
    { value: 'gas_station', label: 'Gas Station' },
    { value: 'bank', label: 'Bank' },
    { value: 'hospital', label: 'Hospital' },
    { value: 'pharmacy', label: 'Pharmacy' },
    { value: 'gym', label: 'Gym' },
    { value: 'beauty_salon', label: 'Beauty Salon' },
    { value: 'car_repair', label: 'Car Repair' },
    { value: 'dentist', label: 'Dentist' },
    { value: 'doctor', label: 'Doctor' },
    { value: 'lawyer', label: 'Lawyer' },
    { value: 'real_estate_agency', label: 'Real Estate Agency' }
  ]

  const availableFields = [
    { field: 'name', label: 'Name', sku: 'Basic', required: true },
    { field: 'formatted_address', label: 'Address', sku: 'Basic' },
    { field: 'geometry', label: 'Location', sku: 'Basic' },
    { field: 'place_id', label: 'Place ID', sku: 'Basic' },
    { field: 'types', label: 'Types', sku: 'Basic' },
    { field: 'business_status', label: 'Business Status', sku: 'Contact' },
    { field: 'formatted_phone_number', label: 'Phone Number', sku: 'Contact' },
    { field: 'international_phone_number', label: 'International Phone', sku: 'Contact' },
    { field: 'website', label: 'Website', sku: 'Contact' },
    { field: 'opening_hours', label: 'Opening Hours', sku: 'Contact' },
    { field: 'rating', label: 'Rating', sku: 'Atmosphere' },
    { field: 'user_ratings_total', label: 'Total Ratings', sku: 'Atmosphere' },
    { field: 'reviews', label: 'Reviews', sku: 'Atmosphere' },
    { field: 'price_level', label: 'Price Level', sku: 'Atmosphere' }
  ]

  const handleSubmit = (e) => {
    e.preventDefault()
    
    if (formData.search_type === 'text_search' && !formData.query) {
      alert('Please enter a search query for text search')
      return
    }
    
    if (formData.search_type === 'nearby_search' && !formData.location) {
      alert('Please enter a location for nearby search')
      return
    }
    
    onSearch(formData)
  }

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleFieldToggle = (field) => {
    setFormData(prev => ({
      ...prev,
      fields: prev.fields.includes(field)
        ? prev.fields.filter(f => f !== field)
        : [...prev.fields, field]
    }))
  }

  const getSkuColor = (sku) => {
    switch (sku) {
      case 'Basic': return 'text-green-400'
      case 'Contact': return 'text-yellow-400'
      case 'Atmosphere': return 'text-red-400'
      default: return 'text-dark-400'
    }
  }

  return (
    <div>
      <div className="flex items-center mb-6">
        <MapPin className="h-6 w-6 text-blue-500 mr-3" />
        <h2 className="text-xl font-semibold text-white">Google Places Search</h2>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Search Type */}
        <div>
          <label className="block text-sm font-medium text-dark-300 mb-3">
            Search Type *
          </label>
          <div className="space-y-2">
            {searchTypes.map((type) => (
              <label key={type.value} className="flex items-center">
                <input
                  type="radio"
                  name="search_type"
                  value={type.value}
                  checked={formData.search_type === type.value}
                  onChange={(e) => handleInputChange('search_type', e.target.value)}
                  className="mr-3 text-blue-500"
                />
                <div>
                  <span className="text-white font-medium">{type.label}</span>
                  <p className="text-xs text-dark-400">{type.description}</p>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Query (for text search) */}
        {formData.search_type === 'text_search' && (
          <div>
            <label className="block text-sm font-medium text-dark-300 mb-2">
              Search Query *
            </label>
            <input
              type="text"
              value={formData.query}
              onChange={(e) => handleInputChange('query', e.target.value)}
              placeholder="e.g., restaurants in New York"
              className="input-field w-full"
              required
            />
          </div>
        )}

        {/* Location */}
        <div>
          <label className="block text-sm font-medium text-dark-300 mb-2">
            Location {formData.search_type === 'nearby_search' ? '*' : '(Optional)'}
          </label>
          <input
            type="text"
            value={formData.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
            placeholder="Address or lat,lng coordinates"
            className="input-field w-full"
            required={formData.search_type === 'nearby_search'}
          />
          <p className="text-xs text-dark-400 mt-1">
            Enter an address or coordinates (e.g., "40.7128,-74.0060")
          </p>
        </div>

        {/* Radius (for nearby search) */}
        {formData.search_type === 'nearby_search' && (
          <div>
            <label className="block text-sm font-medium text-dark-300 mb-2">
              Search Radius: {formData.radius}m
            </label>
            <input
              type="range"
              min="100"
              max="50000"
              step="100"
              value={formData.radius}
              onChange={(e) => handleInputChange('radius', parseInt(e.target.value))}
              className="w-full h-2 bg-dark-700 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-dark-400 mt-1">
              <span>100m</span>
              <span>50km</span>
            </div>
          </div>
        )}

        {/* Place Type */}
        <div>
          <label className="block text-sm font-medium text-dark-300 mb-2">
            Place Type (Optional)
          </label>
          <select
            value={formData.place_type}
            onChange={(e) => handleInputChange('place_type', e.target.value)}
            className="select-field w-full"
          >
            {placeTypes.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        {/* Fields Selection */}
        <div>
          <label className="block text-sm font-medium text-dark-300 mb-3">
            Fields to Return
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 max-h-48 overflow-y-auto">
            {availableFields.map((fieldInfo) => (
              <label key={fieldInfo.field} className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.fields.includes(fieldInfo.field)}
                  onChange={() => handleFieldToggle(fieldInfo.field)}
                  disabled={fieldInfo.required}
                  className="mr-2 text-blue-500"
                />
                <span className="text-white text-sm">{fieldInfo.label}</span>
                <span className={`ml-2 text-xs ${getSkuColor(fieldInfo.sku)}`}>
                  ({fieldInfo.sku})
                </span>
              </label>
            ))}
          </div>
          <div className="mt-2 flex items-center text-xs text-dark-400">
            <Info className="h-3 w-3 mr-1" />
            Different field groups have different pricing. Basic fields are cheapest.
          </div>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={loading}
          className="btn-primary w-full flex items-center justify-center"
        >
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Searching Google Places...
            </>
          ) : (
            <>
              <MapPin className="h-4 w-4 mr-2" />
              Search Google Places
            </>
          )}
        </button>
      </form>

      {/* Info Box */}
      <div className="mt-6 p-4 bg-dark-700 rounded-lg border border-dark-600">
        <h3 className="text-sm font-medium text-white mb-2">About Google Places API</h3>
        <ul className="text-xs text-dark-300 space-y-1">
          <li>• Uses official Google Places API (New)</li>
          <li>• Pricing varies by field type: Basic &lt; Contact &lt; Atmosphere</li>
          <li>• Text search: broad queries, Nearby search: location-based</li>
          <li>• Results are real-time and highly accurate</li>
        </ul>
      </div>
    </div>
  )
}

export default GooglePlacesTab
