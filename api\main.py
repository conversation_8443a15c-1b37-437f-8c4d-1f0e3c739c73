from fastapi import <PERSON>AP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import os
from dotenv import load_dotenv
import logging

# Import routers
from routers import yellowpages, google_places, overpass

# Load environment variables from parent directory
load_dotenv("../.env")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Lead Generation API",
    description="API for lead generation using Yellow Pages, Google Places, and Overpass APIs",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:5173", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(yellowpages.router, prefix="/api/yellowpages", tags=["Yellow Pages"])
app.include_router(google_places.router, prefix="/api/google-places", tags=["Google Places"])
app.include_router(overpass.router, prefix="/api/overpass", tags=["Overpass"])

@app.get("/")
async def root():
    return {"message": "Lead Generation API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "message": "API is running"}

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"Global exception: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"success": False, "error": "Internal server error", "message": str(exc)}
    )

if __name__ == "__main__":
    import uvicorn

    # Check for required environment variables
    google_api_key = os.getenv("GOOGLE_PLACES_API_KEY")
    if not google_api_key:
        logger.warning("GOOGLE_PLACES_API_KEY not found in environment variables")

    openai_api_key = os.getenv("OPENAI_API_KEY")
    if not openai_api_key:
        logger.warning("OPENAI_API_KEY not found in environment variables")

    uvicorn.run(
        "main:app",
        host=os.getenv("BACKEND_HOST", "localhost"),
        port=int(os.getenv("BACKEND_PORT", 8001)),
        reload=True
    )
