import axios from 'axios'

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://localhost:8001/api',
  timeout: 300000, // 5 minutes timeout for long-running searches
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`Making ${config.method.toUpperCase()} request to ${config.url}`)
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    console.error('API Error:', error)
    
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.detail || error.response.data?.message || 'Server error'
      throw new Error(message)
    } else if (error.request) {
      // Request was made but no response received
      throw new Error('No response from server. Please check if the backend is running.')
    } else {
      // Something else happened
      throw new Error(error.message || 'An unexpected error occurred')
    }
  }
)

// Yellow Pages API
export const yellowPagesAPI = {
  search: async (searchData) => {
    const response = await api.post('/yellowpages/search', searchData)
    return response.data
  },
  
  getBusinessTypes: async () => {
    const response = await api.get('/yellowpages/business-types')
    return response.data
  }
}

// Google Places API
export const googlePlacesAPI = {
  search: async (searchData) => {
    const response = await api.post('/google-places/search', searchData)
    return response.data
  },
  
  getPlaceDetails: async (placeId, fields) => {
    const response = await api.post('/google-places/details', {
      place_id: placeId,
      fields: fields
    })
    return response.data
  },
  
  getPlaceTypes: async () => {
    const response = await api.get('/google-places/place-types')
    return response.data
  },
  
  getAvailableFields: async () => {
    const response = await api.get('/google-places/available-fields')
    return response.data
  }
}

// Overpass API
export const overpassAPI = {
  search: async (searchData) => {
    const response = await api.post('/overpass/search', searchData)
    return response.data
  },
  
  customQuery: async (query) => {
    const response = await api.post('/overpass/custom-query', { query })
    return response.data
  },
  
  getAmenityTypes: async () => {
    const response = await api.get('/overpass/amenity-types')
    return response.data
  },
  
  getShopTypes: async () => {
    const response = await api.get('/overpass/shop-types')
    return response.data
  }
}

// Generic search function that routes to the appropriate API
export const searchLeads = async (searchData, source) => {
  switch (source) {
    case 'yellowpages':
      return await yellowPagesAPI.search(searchData)
    
    case 'google':
      return await googlePlacesAPI.search(searchData)
    
    case 'overpass':
      if (searchData.query) {
        // Custom query
        return await overpassAPI.customQuery(searchData.query)
      } else {
        // Simple search
        return await overpassAPI.search(searchData)
      }
    
    default:
      throw new Error(`Unknown search source: ${source}`)
  }
}

// Health check function
export const healthCheck = async () => {
  try {
    const response = await api.get('/health')
    return response.data
  } catch (error) {
    throw new Error('Backend server is not responding')
  }
}

export default api
