import { useState } from 'react'
import { Download, Grid, List, Loader2, AlertCircle, ExternalLink, Phone, Globe, MapPin } from 'lucide-react'

const ResultsDisplay = ({ results, loading, error, source }) => {
  const [viewMode, setViewMode] = useState('cards') // 'cards' or 'table'

  const exportToCSV = () => {
    if (!results || results.length === 0) return

    const headers = ['Name', 'Address', 'Phone', 'Website', 'Description', 'Source']
    const csvContent = [
      headers.join(','),
      ...results.map(item => [
        `"${(item.name || '').replace(/"/g, '""')}"`,
        `"${(item.address || '').replace(/"/g, '""')}"`,
        `"${(item.phone || '').replace(/"/g, '""')}"`,
        `"${(item.website || '').replace(/"/g, '""')}"`,
        `"${(item.description || '').replace(/"/g, '""')}"`,
        `"${(item.source || '').replace(/"/g, '""')}"`
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `lead-generation-${source}-${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const exportToJSON = () => {
    if (!results || results.length === 0) return

    const jsonContent = JSON.stringify(results, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `lead-generation-${source}-${new Date().toISOString().split('T')[0]}.json`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const renderCardView = () => (
    <div className="space-y-4">
      {results.map((item, index) => (
        <div key={index} className="bg-dark-700 border border-dark-600 rounded-lg p-4">
          <div className="flex justify-between items-start mb-3">
            <h3 className="text-lg font-semibold text-white">{item.name || 'Unnamed Business'}</h3>
            <span className={`px-2 py-1 rounded text-xs font-medium ${
              item.source === 'yellowpages' ? 'bg-yellow-600 text-white' :
              item.source === 'google_places' ? 'bg-blue-600 text-white' :
              item.source === 'overpass' ? 'bg-green-600 text-white' :
              'bg-dark-600 text-dark-300'
            }`}>
              {item.source}
            </span>
          </div>
          
          <div className="space-y-2">
            {item.address && (
              <div className="flex items-start text-dark-300">
                <MapPin className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                <span className="text-sm">{item.address}</span>
              </div>
            )}
            
            {item.phone && (
              <div className="flex items-center text-dark-300">
                <Phone className="h-4 w-4 mr-2 flex-shrink-0" />
                <a href={`tel:${item.phone}`} className="text-sm hover:text-blue-400 transition-colors">
                  {item.phone}
                </a>
              </div>
            )}
            
            {item.website && (
              <div className="flex items-center text-dark-300">
                <Globe className="h-4 w-4 mr-2 flex-shrink-0" />
                <a 
                  href={item.website.startsWith('http') ? item.website : `https://${item.website}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm hover:text-blue-400 transition-colors flex items-center"
                >
                  {item.website}
                  <ExternalLink className="h-3 w-3 ml-1" />
                </a>
              </div>
            )}
            
            {item.description && (
              <div className="text-sm text-dark-400 mt-2">
                {item.description}
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  )

  const renderTableView = () => (
    <div className="overflow-x-auto">
      <table className="w-full text-sm">
        <thead>
          <tr className="border-b border-dark-600">
            <th className="text-left py-3 px-2 text-dark-300 font-medium">Name</th>
            <th className="text-left py-3 px-2 text-dark-300 font-medium">Address</th>
            <th className="text-left py-3 px-2 text-dark-300 font-medium">Phone</th>
            <th className="text-left py-3 px-2 text-dark-300 font-medium">Website</th>
            <th className="text-left py-3 px-2 text-dark-300 font-medium">Source</th>
          </tr>
        </thead>
        <tbody>
          {results.map((item, index) => (
            <tr key={index} className="border-b border-dark-700 hover:bg-dark-800 transition-colors">
              <td className="py-3 px-2 text-white font-medium">{item.name || 'Unnamed'}</td>
              <td className="py-3 px-2 text-dark-300">{item.address || '-'}</td>
              <td className="py-3 px-2 text-dark-300">
                {item.phone ? (
                  <a href={`tel:${item.phone}`} className="hover:text-blue-400 transition-colors">
                    {item.phone}
                  </a>
                ) : '-'}
              </td>
              <td className="py-3 px-2 text-dark-300">
                {item.website ? (
                  <a 
                    href={item.website.startsWith('http') ? item.website : `https://${item.website}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:text-blue-400 transition-colors flex items-center"
                  >
                    Visit
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                ) : '-'}
              </td>
              <td className="py-3 px-2">
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  item.source === 'yellowpages' ? 'bg-yellow-600 text-white' :
                  item.source === 'google_places' ? 'bg-blue-600 text-white' :
                  item.source === 'overpass' ? 'bg-green-600 text-white' :
                  'bg-dark-600 text-dark-300'
                }`}>
                  {item.source}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-white">Search Results</h2>
        
        {results && results.length > 0 && (
          <div className="flex items-center space-x-2">
            {/* View Mode Toggle */}
            <div className="flex space-x-1 bg-dark-800 p-1 rounded-lg">
              <button
                onClick={() => setViewMode('cards')}
                className={`p-2 rounded transition-colors ${
                  viewMode === 'cards' ? 'bg-blue-600 text-white' : 'text-dark-400 hover:text-white'
                }`}
              >
                <Grid className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('table')}
                className={`p-2 rounded transition-colors ${
                  viewMode === 'table' ? 'bg-blue-600 text-white' : 'text-dark-400 hover:text-white'
                }`}
              >
                <List className="h-4 w-4" />
              </button>
            </div>

            {/* Export Buttons */}
            <div className="flex space-x-2">
              <button
                onClick={exportToCSV}
                className="btn-secondary flex items-center text-sm"
              >
                <Download className="h-4 w-4 mr-1" />
                CSV
              </button>
              <button
                onClick={exportToJSON}
                className="btn-secondary flex items-center text-sm"
              >
                <Download className="h-4 w-4 mr-1" />
                JSON
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="min-h-[400px]">
        {loading && (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-4" />
              <p className="text-dark-300">Searching for leads...</p>
            </div>
          </div>
        )}

        {error && (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
              <p className="text-red-400 mb-2">Search Error</p>
              <p className="text-dark-400 text-sm">{error}</p>
            </div>
          </div>
        )}

        {!loading && !error && (!results || results.length === 0) && (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="h-8 w-8 bg-dark-700 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-dark-400 text-sm">?</span>
              </div>
              <p className="text-dark-300 mb-2">No results yet</p>
              <p className="text-dark-400 text-sm">Use the search form to find leads</p>
            </div>
          </div>
        )}

        {!loading && !error && results && results.length > 0 && (
          <div>
            <div className="mb-4 text-sm text-dark-400">
              Found {results.length} result{results.length !== 1 ? 's' : ''}
            </div>
            {viewMode === 'cards' ? renderCardView() : renderTableView()}
          </div>
        )}
      </div>
    </div>
  )
}

export default ResultsDisplay
