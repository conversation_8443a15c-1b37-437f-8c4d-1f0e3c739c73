import { useState } from 'react'
import { Database, Loader2, Code, MapPin } from 'lucide-react'

const OverpassTab = ({ onSearch, loading }) => {
  const [queryMode, setQueryMode] = useState('simple') // 'simple' or 'custom'
  const [formData, setFormData] = useState({
    query_type: 'amenity',
    amenity: '',
    shop: '',
    location_type: 'center',
    center: '',
    radius: 1000,
    bbox: '',
    tags: {},
    custom_query: ''
  })

  const amenityTypes = [
    { value: '', label: 'Select amenity...' },
    { value: 'restaurant', label: 'Restaurant' },
    { value: 'cafe', label: 'Cafe' },
    { value: 'bar', label: 'Bar' },
    { value: 'pub', label: 'Pub' },
    { value: 'fast_food', label: 'Fast Food' },
    { value: 'hotel', label: 'Hotel' },
    { value: 'bank', label: 'Bank' },
    { value: 'hospital', label: 'Hospital' },
    { value: 'pharmacy', label: 'Pharmacy' },
    { value: 'fuel', label: 'Gas Station' },
    { value: 'dentist', label: 'Dentist' },
    { value: 'doctors', label: 'Doctor' },
    { value: 'veterinary', label: 'Veterinary' },
    { value: 'gym', label: 'Gym' },
    { value: 'beauty_salon', label: 'Beauty Salon' },
    { value: 'hairdresser', label: 'Hairdresser' }
  ]

  const shopTypes = [
    { value: '', label: 'Select shop type...' },
    { value: 'supermarket', label: 'Supermarket' },
    { value: 'convenience', label: 'Convenience Store' },
    { value: 'bakery', label: 'Bakery' },
    { value: 'butcher', label: 'Butcher' },
    { value: 'clothes', label: 'Clothing Store' },
    { value: 'shoes', label: 'Shoe Store' },
    { value: 'electronics', label: 'Electronics Store' },
    { value: 'books', label: 'Bookstore' },
    { value: 'florist', label: 'Florist' },
    { value: 'hardware', label: 'Hardware Store' },
    { value: 'car_repair', label: 'Car Repair' },
    { value: 'bicycle', label: 'Bicycle Shop' },
    { value: 'jewelry', label: 'Jewelry Store' },
    { value: 'optician', label: 'Optician' },
    { value: 'pet', label: 'Pet Store' }
  ]

  const handleSubmit = (e) => {
    e.preventDefault()
    
    if (queryMode === 'custom') {
      if (!formData.custom_query.trim()) {
        alert('Please enter a custom Overpass QL query')
        return
      }
      onSearch({ query: formData.custom_query }, 'custom')
      return
    }

    // Simple mode validation
    if (formData.query_type === 'amenity' && !formData.amenity) {
      alert('Please select an amenity type')
      return
    }
    
    if (formData.query_type === 'shop' && !formData.shop) {
      alert('Please select a shop type')
      return
    }

    if (formData.location_type === 'center' && !formData.center) {
      alert('Please enter a center location')
      return
    }

    if (formData.location_type === 'bbox' && !formData.bbox) {
      alert('Please enter a bounding box')
      return
    }

    // Build search data
    const searchData = {
      [formData.query_type]: formData.query_type === 'amenity' ? formData.amenity : formData.shop
    }

    if (formData.location_type === 'center') {
      const coords = formData.center.split(',').map(c => parseFloat(c.trim()))
      if (coords.length === 2 && !isNaN(coords[0]) && !isNaN(coords[1])) {
        searchData.center = coords
        searchData.radius = formData.radius
      }
    } else if (formData.location_type === 'bbox') {
      const bbox = formData.bbox.split(',').map(c => parseFloat(c.trim()))
      if (bbox.length === 4 && bbox.every(c => !isNaN(c))) {
        searchData.bbox = bbox
      }
    }

    if (Object.keys(formData.tags).length > 0) {
      searchData.tags = formData.tags
    }

    onSearch(searchData)
  }

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleTagChange = (key, value) => {
    setFormData(prev => ({
      ...prev,
      tags: {
        ...prev.tags,
        [key]: value
      }
    }))
  }

  const removeTag = (key) => {
    setFormData(prev => {
      const newTags = { ...prev.tags }
      delete newTags[key]
      return {
        ...prev,
        tags: newTags
      }
    })
  }

  const addTag = () => {
    const key = prompt('Enter tag key (e.g., "cuisine"):')
    if (key) {
      const value = prompt(`Enter value for "${key}":`)
      if (value) {
        handleTagChange(key, value)
      }
    }
  }

  return (
    <div>
      <div className="flex items-center mb-6">
        <Database className="h-6 w-6 text-green-500 mr-3" />
        <h2 className="text-xl font-semibold text-white">Overpass API Search</h2>
      </div>

      {/* Query Mode Toggle */}
      <div className="mb-6">
        <div className="flex space-x-1 bg-dark-800 p-1 rounded-lg">
          <button
            type="button"
            onClick={() => setQueryMode('simple')}
            className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
              queryMode === 'simple' ? 'bg-green-600 text-white' : 'text-dark-300 hover:text-white'
            }`}
          >
            <MapPin className="h-4 w-4 mr-2" />
            Simple Search
          </button>
          <button
            type="button"
            onClick={() => setQueryMode('custom')}
            className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
              queryMode === 'custom' ? 'bg-green-600 text-white' : 'text-dark-300 hover:text-white'
            }`}
          >
            <Code className="h-4 w-4 mr-2" />
            Custom Query
          </button>
        </div>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {queryMode === 'custom' ? (
          /* Custom Query Mode */
          <div>
            <label className="block text-sm font-medium text-dark-300 mb-2">
              Custom Overpass QL Query *
            </label>
            <textarea
              value={formData.custom_query}
              onChange={(e) => handleInputChange('custom_query', e.target.value)}
              placeholder={`[out:json][timeout:60];
(
  node["amenity"="restaurant"](40.7,-74.1,40.8,-74.0);
);
out geom;`}
              className="input-field w-full h-32 font-mono text-sm"
              required
            />
            <p className="text-xs text-dark-400 mt-1">
              Enter a complete Overpass QL query. See documentation for syntax.
            </p>
          </div>
        ) : (
          /* Simple Mode */
          <>
            {/* Query Type */}
            <div>
              <label className="block text-sm font-medium text-dark-300 mb-3">
                Search For *
              </label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="query_type"
                    value="amenity"
                    checked={formData.query_type === 'amenity'}
                    onChange={(e) => handleInputChange('query_type', e.target.value)}
                    className="mr-3 text-green-500"
                  />
                  <span className="text-white">Amenities (restaurants, banks, etc.)</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="query_type"
                    value="shop"
                    checked={formData.query_type === 'shop'}
                    onChange={(e) => handleInputChange('query_type', e.target.value)}
                    className="mr-3 text-green-500"
                  />
                  <span className="text-white">Shops (stores, markets, etc.)</span>
                </label>
              </div>
            </div>

            {/* Amenity/Shop Selection */}
            {formData.query_type === 'amenity' && (
              <div>
                <label className="block text-sm font-medium text-dark-300 mb-2">
                  Amenity Type *
                </label>
                <select
                  value={formData.amenity}
                  onChange={(e) => handleInputChange('amenity', e.target.value)}
                  className="select-field w-full"
                  required
                >
                  {amenityTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {formData.query_type === 'shop' && (
              <div>
                <label className="block text-sm font-medium text-dark-300 mb-2">
                  Shop Type *
                </label>
                <select
                  value={formData.shop}
                  onChange={(e) => handleInputChange('shop', e.target.value)}
                  className="select-field w-full"
                  required
                >
                  {shopTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* Location Type */}
            <div>
              <label className="block text-sm font-medium text-dark-300 mb-3">
                Location Method *
              </label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="location_type"
                    value="center"
                    checked={formData.location_type === 'center'}
                    onChange={(e) => handleInputChange('location_type', e.target.value)}
                    className="mr-3 text-green-500"
                  />
                  <span className="text-white">Center Point + Radius</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="location_type"
                    value="bbox"
                    checked={formData.location_type === 'bbox'}
                    onChange={(e) => handleInputChange('location_type', e.target.value)}
                    className="mr-3 text-green-500"
                  />
                  <span className="text-white">Bounding Box</span>
                </label>
              </div>
            </div>

            {/* Center Point */}
            {formData.location_type === 'center' && (
              <>
                <div>
                  <label className="block text-sm font-medium text-dark-300 mb-2">
                    Center Point (lat,lng) *
                  </label>
                  <input
                    type="text"
                    value={formData.center}
                    onChange={(e) => handleInputChange('center', e.target.value)}
                    placeholder="e.g., 40.7128,-74.0060"
                    className="input-field w-full"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-dark-300 mb-2">
                    Radius: {formData.radius}m
                  </label>
                  <input
                    type="range"
                    min="100"
                    max="10000"
                    step="100"
                    value={formData.radius}
                    onChange={(e) => handleInputChange('radius', parseInt(e.target.value))}
                    className="w-full h-2 bg-dark-700 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-dark-400 mt-1">
                    <span>100m</span>
                    <span>10km</span>
                  </div>
                </div>
              </>
            )}

            {/* Bounding Box */}
            {formData.location_type === 'bbox' && (
              <div>
                <label className="block text-sm font-medium text-dark-300 mb-2">
                  Bounding Box (south,west,north,east) *
                </label>
                <input
                  type="text"
                  value={formData.bbox}
                  onChange={(e) => handleInputChange('bbox', e.target.value)}
                  placeholder="e.g., 40.7,-74.1,40.8,-74.0"
                  className="input-field w-full"
                  required
                />
                <p className="text-xs text-dark-400 mt-1">
                  Format: south,west,north,east coordinates
                </p>
              </div>
            )}

            {/* Additional Tags */}
            <div>
              <label className="block text-sm font-medium text-dark-300 mb-2">
                Additional Tags (Optional)
              </label>
              <div className="space-y-2">
                {Object.entries(formData.tags).map(([key, value]) => (
                  <div key={key} className="flex items-center space-x-2">
                    <span className="text-sm text-white bg-dark-700 px-2 py-1 rounded">
                      {key}={value}
                    </span>
                    <button
                      type="button"
                      onClick={() => removeTag(key)}
                      className="text-red-400 hover:text-red-300 text-sm"
                    >
                      Remove
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addTag}
                  className="text-green-400 hover:text-green-300 text-sm"
                >
                  + Add Tag
                </button>
              </div>
              <p className="text-xs text-dark-400 mt-1">
                Add custom OSM tags to filter results (e.g., cuisine=italian)
              </p>
            </div>
          </>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          disabled={loading}
          className="btn-primary w-full flex items-center justify-center"
        >
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Querying Overpass API...
            </>
          ) : (
            <>
              <Database className="h-4 w-4 mr-2" />
              Search Overpass API
            </>
          )}
        </button>
      </form>

      {/* Info Box */}
      <div className="mt-6 p-4 bg-dark-700 rounded-lg border border-dark-600">
        <h3 className="text-sm font-medium text-white mb-2">About Overpass API</h3>
        <ul className="text-xs text-dark-300 space-y-1">
          <li>• Searches OpenStreetMap data using Overpass QL</li>
          <li>• Free to use but has rate limits</li>
          <li>• Data quality depends on OSM contributors</li>
          <li>• Custom queries allow advanced filtering</li>
        </ul>
      </div>
    </div>
  )
}

export default OverpassTab
