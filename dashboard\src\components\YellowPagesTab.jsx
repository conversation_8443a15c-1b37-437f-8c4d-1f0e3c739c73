import { useState } from 'react'
import { Search, Loader2 } from 'lucide-react'

const YellowPagesTab = ({ onSearch, loading }) => {
  const [formData, setFormData] = useState({
    business_type: '',
    location: '',
    max_pages: 3
  })

  const businessTypes = [
    { value: 'restaurants', label: 'Restaurants' },
    { value: 'dentists', label: 'Dentists' },
    { value: 'lawyers', label: 'Lawyers' },
    { value: 'doctors', label: 'Doctors' },
    { value: 'plumbers', label: 'Plumbers' },
    { value: 'electricians', label: 'Electricians' },
    { value: 'real_estate', label: 'Real Estate' },
    { value: 'auto_repair', label: 'Auto Repair' },
    { value: 'hair_salons', label: 'Hair Salons' },
    { value: 'veterinarians', label: 'Veterinarians' },
    { value: 'accountants', label: 'Accountants' },
    { value: 'insurance', label: 'Insurance' },
    { value: 'hotels', label: 'Hotels' },
    { value: 'gyms', label: 'Gyms' },
    { value: 'pharmacies', label: 'Pharmacies' },
    { value: 'banks', label: 'Banks' },
    { value: 'gas_stations', label: 'Gas Stations' },
    { value: 'grocery_stores', label: 'Grocery Stores' },
    { value: 'florists', label: 'Florists' },
    { value: 'photographers', label: 'Photographers' },
    { value: 'contractors', label: 'Contractors' },
    { value: 'chiropractors', label: 'Chiropractors' }
  ]

  const handleSubmit = (e) => {
    e.preventDefault()
    if (!formData.business_type || !formData.location) {
      alert('Please fill in all required fields')
      return
    }
    onSearch(formData)
  }

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <div>
      <div className="flex items-center mb-6">
        <Search className="h-6 w-6 text-yellow-500 mr-3" />
        <h2 className="text-xl font-semibold text-white">Yellow Pages Search</h2>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Business Type */}
        <div>
          <label className="block text-sm font-medium text-dark-300 mb-2">
            Business Type *
          </label>
          <select
            value={formData.business_type}
            onChange={(e) => handleInputChange('business_type', e.target.value)}
            className="select-field w-full"
            required
          >
            <option value="">Select business type...</option>
            {businessTypes.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        {/* Location */}
        <div>
          <label className="block text-sm font-medium text-dark-300 mb-2">
            Location *
          </label>
          <input
            type="text"
            value={formData.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
            placeholder="e.g., Toronto ON, New York NY"
            className="input-field w-full"
            required
          />
          <p className="text-xs text-dark-400 mt-1">
            Enter city and state/province (e.g., "Toronto ON", "New York NY")
          </p>
        </div>

        {/* Max Pages */}
        <div>
          <label className="block text-sm font-medium text-dark-300 mb-2">
            Maximum Pages to Scrape: {formData.max_pages}
          </label>
          <input
            type="range"
            min="1"
            max="10"
            value={formData.max_pages}
            onChange={(e) => handleInputChange('max_pages', parseInt(e.target.value))}
            className="w-full h-2 bg-dark-700 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-xs text-dark-400 mt-1">
            <span>1 page</span>
            <span>10 pages</span>
          </div>
          <p className="text-xs text-dark-400 mt-1">
            More pages = more results but longer processing time
          </p>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={loading}
          className="btn-primary w-full flex items-center justify-center"
        >
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Searching Yellow Pages...
            </>
          ) : (
            <>
              <Search className="h-4 w-4 mr-2" />
              Search Yellow Pages
            </>
          )}
        </button>
      </form>

      {/* Info Box */}
      <div className="mt-6 p-4 bg-dark-700 rounded-lg border border-dark-600">
        <h3 className="text-sm font-medium text-white mb-2">About Yellow Pages Search</h3>
        <ul className="text-xs text-dark-300 space-y-1">
          <li>• Searches Canadian Yellow Pages (yellowpages.ca)</li>
          <li>• Extracts business name, address, phone, website, and description</li>
          <li>• Uses AI to clean and structure the data</li>
          <li>• Results may take 1-3 minutes depending on pages selected</li>
        </ul>
      </div>
    </div>
  )
}

export default YellowPagesTab
