from fastapi import FastAPI
import uvicorn
import os
from dotenv import load_dotenv

# Load environment variables from parent directory
load_dotenv("../.env")

app = FastAPI(title="Test Server")

@app.get("/")
async def root():
    return {"message": "Test server is running"}

@app.get("/health")
async def health():
    return {"status": "healthy"}

if __name__ == "__main__":
    print("Starting test server on port 8001...")
    uvicorn.run("test_server:app", host="localhost", port=8001, reload=True)
